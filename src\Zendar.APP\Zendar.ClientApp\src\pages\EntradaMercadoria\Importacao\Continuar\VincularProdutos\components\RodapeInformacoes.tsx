import React from 'react';
import { Flex, HStack, Text, Divider } from '@chakra-ui/react';

import { DecimalMask } from 'helpers/format/fieldsMasks';
import { usePadronizacaoContext } from 'store/Padronizacao/Padronizacao';

import { InformacoesRodape } from '../hooks/useProdutosVinculacao';

interface RodapeInformacoesProps {
  informacoesRodape: InformacoesRodape;
}

export function RodapeInformacoes({ informacoesRodape }: RodapeInformacoesProps) {
  const { casasDecimais } = usePadronizacaoContext();

  return (
    <Flex
      alignItems="center"
      justifyContent="space-between"
      mt={{ base: 4, sm: 6, md: 8 }}
      fontSize="sm"
    >
      <HStack spacing="6" h="full" lineHeight="none">
        <Flex>
          <Text fontWeight="light" color="gray.700">
            Total de produtos:
          </Text>
          <Text ml="2" fontWeight="bold">
            {DecimalMask(
              informacoesRodape.totalProdutos,
              casasDecimais.casasDecimaisQuantidade,
              casasDecimais.casasDecimaisQuantidade
            )}
          </Text>
        </Flex>
        <Divider orientation="vertical" h="6" />
        <Flex>
          <Text fontWeight="light">Quantidade de itens:</Text>
          <Text ml="2" fontWeight="bold">
            {DecimalMask(
              informacoesRodape.quantidadeItens,
              casasDecimais.casasDecimaisQuantidade,
              casasDecimais.casasDecimaisQuantidade
            )}
          </Text>
        </Flex>
      </HStack>
      <Flex flexDir="column">
        <Text fontWeight="bold">Valor total de produtos</Text>
        <Flex gap="0px" minW="250px" height="35px">
          <Flex
            fontSize="12px"
            color="gray.700"
            borderRadius="4px 0px 0px 4px"
            borderLeft="1px"
            borderTop="1px"
            borderBottom="1px"
            borderColor="gray.200"
            borderStyle="solid"
            minW="34px"
            alignItems="center"
            justifyContent="center"
            as="p"
          >
            R$
          </Flex>
          <Flex
            color="black"
            width="calc(100% - 34px)"
            fontSize="14px"
            borderRadius="0px 4px 4px 0px"
            border="1px"
            borderColor="gray.200"
            borderStyle="solid"
            alignItems="center"
            justifyContent="flex-start"
            as="p"
            pl="16px"
          >
            {DecimalMask(informacoesRodape.valorTotalProdutos, 2, 2)}
          </Flex>
        </Flex>
      </Flex>
    </Flex>
  );
}
