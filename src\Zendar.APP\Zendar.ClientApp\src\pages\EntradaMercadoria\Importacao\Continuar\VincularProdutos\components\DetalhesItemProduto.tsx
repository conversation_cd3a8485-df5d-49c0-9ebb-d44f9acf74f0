import React from 'react';
import { Box, Divider, Flex, Text, HStack } from '@chakra-ui/react';

import { Produto, EntradaMercadoriaStatusVinculoProduto } from '../hooks/useProdutosVinculacao';

interface DetalhesItemProdutoProps {
  produto: Produto;
  obterCorBackground: (status: EntradaMercadoriaStatusVinculoProduto) => string;
}

export function DetalhesItemProduto({ produto, obterCorBackground }: DetalhesItemProdutoProps) {
  const codigoBarrasCadastroDiferente = !produto.produtoVinculado?.codigoBarrasCadastro
    ? false
    : produto.produtoVinculado?.codigoBarrasNota !==
      produto.produtoVinculado?.codigoBarrasCadastro;

  return (
    <Box
      h="68px"
      borderBottomRadius="md"
      bg={obterCorBackground(produto.statusVinculo)}
      color={
        produto.statusVinculo === EntradaMercadoriaStatusVinculoProduto.VINCULADO
          ? 'white'
          : 'inherit'
      }
      px="5"
      pt={produto.dadosAdicionais ? '32px' : '0px'}
      transition="all 0.3s"
    >
      <Divider />
      <Flex h="full" px="5" align="start" py="16px" justify="space-between">
        <Text
          fontWeight="bold"
          textColor={
            produto.statusVinculo ===
            EntradaMercadoriaStatusVinculoProduto.PENDENTE_INFORMAR_VARIACOES
              ? 'gray.700'
              : 'secondary.300'
          }
          fontSize="12px"
        >
          <Text as="span" fontWeight="light">
            Produto vinculado:{' '}
          </Text>
          {produto.produtoVinculado?.nome}
        </Text>
        <Flex gap={{ base: '32px', lg: '164px' }}>
          <HStack gap={{ base: '32px', lg: '164px' }}>
            <Box
              as="table"
              sx={{
                bg: obterCorBackground(produto.statusVinculo),
                '& tr': {
                  borderRadius: 'md',
                  boxShadow: 'none',
                },
              }}
            >
              <Box
                as="tr"
                sx={{
                  '& td': {
                    bg: obterCorBackground(produto.statusVinculo),
                    height: '22px !important',
                    padding: '0px !important',
                    paddingLeft: '6px !important',
                  },
                  '& td:first-of-type': {
                    paddingLeft: '0px !important',
                  },
                }}
              >
                <Box as="td" textAlign="right">
                  <Text fontWeight="light" whiteSpace="nowrap">
                    Conversão:{' '}
                  </Text>
                </Box>
                <Box as="td">
                  <Text fontWeight="bold" fontSize="12px" whiteSpace="nowrap">
                    {produto.produtoVinculado?.conversao}x
                  </Text>
                </Box>
              </Box>
              <Box
                as="tr"
                sx={{
                  '& td': {
                    bg: obterCorBackground(produto.statusVinculo),
                    height: '22px !important',
                    padding: '0px !important',
                    paddingLeft: '6px !important',
                  },
                  '& td:first-of-type': {
                    paddingLeft: '0px !important',
                  },
                }}
              >
                <Box as="td" textAlign="right">
                  <Text fontWeight="light" whiteSpace="nowrap">
                    Nova Quantidade:{' '}
                  </Text>
                </Box>
                <Box as="td">
                  <Text fontWeight="bold" whiteSpace="nowrap">
                    {produto.produtoVinculado?.novaQuantidade}
                  </Text>
                </Box>
              </Box>
            </Box>
            <Box
              as="table"
              sx={{
                bg: obterCorBackground(produto.statusVinculo),
                '& tr': {
                  borderRadius: 'md',
                  boxShadow: 'none',
                },
              }}
            >
              <Box
                as="tr"
                sx={{
                  '& td': {
                    bg: obterCorBackground(produto.statusVinculo),
                    height: '22px !important',
                    padding: '0px !important',
                    paddingLeft: '6px !important',
                  },
                  '& td:first-of-type': {
                    paddingLeft: '6px !important',
                  },
                }}
              >
                <Box as="td" textAlign="right">
                  <Text fontWeight="light" whiteSpace="nowrap">
                    Código de barras da nota:{' '}
                  </Text>
                </Box>
                <Box as="td">
                  <Text
                    fontWeight="bold"
                    fontSize="12px"
                    whiteSpace="nowrap"
                    paddingRight="6px !important"
                  >
                    {produto.produtoVinculado?.codigoBarrasNota || 'Não informado'}
                  </Text>
                </Box>
              </Box>
              <Box
                as="tr"
                sx={{
                  '& td': {
                    bg: obterCorBackground(produto.statusVinculo),
                    height: '22px !important',
                    padding: '0px !important',
                    paddingLeft: '0px !important',
                    textColor: codigoBarrasCadastroDiferente ? 'black' : 'inherit',
                  },
                  '& td:first-of-type': {
                    paddingLeft: '0px !important',
                  },
                }}
              >
                <Box as="td" textAlign="right">
                  <Text
                    width="min"
                    height="full"
                    fontWeight="light"
                    whiteSpace="nowrap"
                    paddingLeft="6px !important"
                    bg={
                      codigoBarrasCadastroDiferente
                        ? 'yellow.600'
                        : obterCorBackground(produto.statusVinculo)
                    }
                  >
                    Código de barras do cadastro:{' '}
                  </Text>
                </Box>
                <Box as="td">
                  <Text
                    width="min"
                    height="full"
                    lineHeight="full"
                    fontWeight="bold"
                    whiteSpace="nowrap"
                    paddingX="6px !important"
                    bg={
                      codigoBarrasCadastroDiferente
                        ? 'yellow.600'
                        : obterCorBackground(produto.statusVinculo)
                    }
                  >
                    {produto.produtoVinculado?.codigoBarrasCadastro || 'Não informado'}
                  </Text>
                </Box>
              </Box>
            </Box>
          </HStack>
        </Flex>
      </Flex>
    </Box>
  );
}
